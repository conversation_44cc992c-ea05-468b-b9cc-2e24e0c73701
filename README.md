# Prekliate Dedičstvo

Dobrodružná hra plná hlavolamov a záhad vytvorená v Godot Engine.

## Popis hry

**Prekliate Dedičstvo** je prí<PERSON>hová hra, kde hráč zdedí starý hrad plný záhad. Hra obsahuje 6 kapitol plus e<PERSON><PERSON><PERSON><PERSON>, ka<PERSON><PERSON><PERSON> kapitola má 2 hlavolamy a bohatý dialógový systém s rozprávačom.

## Funkcie

- **Main Menu** s možnosťami: Nov<PERSON> hra, <PERSON><PERSON><PERSON>, Nastavenia, O hre
- **6 Kapitol** + Epilóg s progresívnym odomykaním
- **12 Hlavolamov** (2 v kaž<PERSON>j kapitole)
- **Dialógový systém** s rozprávačom
- **Ukladanie progresu** - automatické ukladanie dokončených kapitol a hlavolamov
- **Nastavenia** - hlas<PERSON>sť, celá obrazovka
- **Použitie vlastných UI assetov** z priečinka assets/

## Štruktúra projektu

```
├── project.godot           # Hlavný konfiguračný súbor
├── scenes/                 # Všetky scény
│   ├── MainMenu.tscn      # Hlavné menu
│   ├── ChaptersMenu.tscn  # Menu kapitol
│   ├── SettingsMenu.tscn  # Nastavenia
│   ├── AboutGame.tscn     # O hre
│   ├── DialogueSystem.tscn # Dialógový systém
│   ├── Chapter1.tscn      # Kapitola 1
│   ├── Chapter2.tscn      # Kapitola 2
│   ├── Chapter3.tscn      # Kapitola 3
│   ├── Chapter4.tscn      # Kapitola 4
│   ├── Chapter5.tscn      # Kapitola 5
│   ├── Chapter6.tscn      # Kapitola 6
│   └── Chapter7.tscn      # Epilóg
├── scripts/               # Všetky skripty
│   ├── MainMenu.gd
│   ├── ChaptersMenu.gd
│   ├── SettingsMenu.gd
│   ├── AboutGame.gd
│   ├── DialogueSystem.gd
│   ├── Chapter.gd
│   └── Epilogue.gd
├── autoload/              # Singleton skripty
│   └── GameManager.gd     # Hlavný manažér hry
└── assets/                # UI assety
	├── Buttons/
	├── Scalable screen/
	├── Sliders/
	└── ...
```

## Kapitoly

1. **Kapitola 1: Záhadný začiatok**
   - Hlavolam kľúčov
   - Číselná sekvencia

2. **Kapitola 2: Hlbšie do temnoty**
   - Labyrint tieňov
   - Kódované správy

3. **Kapitola 3: Stredoveké tajomstvá**
   - Rytierske symboly
   - Alchymistická formula

4. **Kapitola 4: Magické rituály**
   - Runy moci
   - Kúzelné kruhy

5. **Kapitola 5: Posledné varovanie**
   - Časová paradox
   - Zrkadlové bludiště

6. **Kapitola 6: Konečné odhalenie**
   - Majstrovský kľúč
   - Posledná hádanka

7. **Epilóg: Nový začiatok**
   - Záverečný príbeh

## Ovládanie

- **Šípky/WASD** - Navigácia v menu
- **Enter/Medzerník** - Potvrdiť/Pokračovať v dialógu
- **Escape** - Späť/Zrušiť

## Spustenie

1. Otvorte projekt v Godot Engine 4.2+
2. Stlačte F5 alebo kliknite na "Play" tlačidlo
3. Hlavná scéna sa automaticky načíta (MainMenu.tscn)

## Technické detaily

- **Engine**: Godot 4.2+
- **Rozlíšenie**: 1920x1080 (fullscreen/windowed)
- **Ukladanie**: JSON súbory v user://
- **UI**: NinePatchRect s vlastnými assetmi
- **Architektúra**: Singleton GameManager pre správu stavu

## Rozšírenia

Projekt je navrhnutý tak, aby sa dal ľahko rozšíriť:

- Pridanie nových kapitol (vytvorenie novej scény + aktualizácia GameManager.chapter_info)
- Implementácia skutočných hlavolamov namiesto simulácie
- Pridanie hudby a zvukových efektov
- Animácie a vizuálne efekty
- Viac typov dialógov a postáv

## Poznámky

- Hlavolamy sú momentálne simulované jednoduchými dialógmi
- Pre plnú funkcionalitu by bolo potrebné implementovať skutočnú logiku hlavolamov
- UI používa assety z priečinka assets/ pre konzistentný vzhľad
- Všetky texty sú v slovenčine
