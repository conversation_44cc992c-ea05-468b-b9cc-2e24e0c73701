extends Control
class_name Chapter

@export var chapter_number: int = 1
@onready var dialogue_system: DialogueSystem = $DialogueSystem
@onready var chapter_title: Label = $VBoxContainer/ChapterTitle
@onready var puzzle1_button: Button = $VBoxContainer/PuzzlesContainer/Puzzle1Button
@onready var puzzle2_button: Button = $VBoxContainer/PuzzlesContainer/Puzzle2Button
@onready var back_button: Button = $VBoxContainer/BackButton
@onready var hint_button: Button = $VBoxContainer/HintButton

var current_puzzle: int = 0
var puzzles_completed: Array[bool] = [false, false]

func _ready():
	# Nastavenie textu
	var chapter_info = GameManager.chapter_info[chapter_number]
	chapter_title.text = chapter_info.title
	
	if chapter_info.puzzles.size() >= 2:
		puzzle1_button.text = chapter_info.puzzles[0]
		puzzle2_button.text = chapter_info.puzzles[1]
	
	# Pripoje<PERSON> sign<PERSON><PERSON>
	puzzle1_button.pressed.connect(_on_puzzle1_pressed)
	puzzle2_button.pressed.connect(_on_puzzle2_pressed)
	back_button.pressed.connect(_on_back_pressed)
	hint_button.pressed.connect(_on_hint_pressed)
	
	dialogue_system.dialogue_finished.connect(_on_dialogue_finished)
	
	# Kontrola stavu hlavolamov
	update_puzzle_buttons()
	
	# Spustenie úvodného dialógu
	start_chapter_intro()
	
	# Nastavenie fokusu
	puzzle1_button.grab_focus()

func start_chapter_intro():
	var intro_dialogue = dialogue_system.get_chapter_intro_dialogue(chapter_number)
	dialogue_system.start_dialogue(intro_dialogue)

func update_puzzle_buttons():
	# Aktualizovanie stavu tlačidiel na základe dokončených hlavolamov
	puzzles_completed[0] = GameManager.is_puzzle_completed(chapter_number, 1)
	puzzles_completed[1] = GameManager.is_puzzle_completed(chapter_number, 2)
	
	if puzzles_completed[0]:
		puzzle1_button.text += " ✓"
		puzzle1_button.modulate = Color.GREEN
	
	if puzzles_completed[1]:
		puzzle2_button.text += " ✓"
		puzzle2_button.modulate = Color.GREEN

func _on_puzzle1_pressed():
	current_puzzle = 1
	start_puzzle(1)

func _on_puzzle2_pressed():
	current_puzzle = 2
	start_puzzle(2)

func start_puzzle(puzzle_number: int):
	# Spustenie dialógu pre hlavolam
	var puzzle_dialogue = dialogue_system.get_puzzle_intro_dialogue(chapter_number, puzzle_number)
	dialogue_system.start_dialogue(puzzle_dialogue)

func _on_dialogue_finished():
	if current_puzzle > 0:
		# Spustenie hlavolamu
		show_puzzle_scene(current_puzzle)

func show_puzzle_scene(puzzle_number: int):
	# Simulácia hlavolamu - v skutočnej hre by tu bola logika hlavolamu
	show_simple_puzzle(puzzle_number)

func show_simple_puzzle(puzzle_number: int):
	# Jednoduchý dialóg pre simuláciu hlavolamu
	var puzzle_scene = AcceptDialog.new()
	add_child(puzzle_scene)
	
	var chapter_info = GameManager.chapter_info[chapter_number]
	var puzzle_name = chapter_info.puzzles[puzzle_number - 1]
	
	puzzle_scene.dialog_text = "Hlavolam: " + puzzle_name + "\n\nToto je simulácia hlavolamu.\nV skutočnej hre by tu bola interaktívna logika.\n\nChcete označiť tento hlavolam ako dokončený?"
	puzzle_scene.title = "Hlavolam " + str(puzzle_number)
	
	# Pridanie tlačidiel
	puzzle_scene.add_button("Dokončiť", true, "complete")
	puzzle_scene.add_button("Zrušiť", false, "cancel")
	
	puzzle_scene.custom_action.connect(_on_puzzle_action)
	puzzle_scene.popup_centered()

func _on_puzzle_action(action: String):
	if action == "complete":
		complete_puzzle(current_puzzle)
	current_puzzle = 0

func complete_puzzle(puzzle_number: int):
	# Označenie hlavolamu ako dokončený
	GameManager.complete_puzzle(chapter_number, puzzle_number)
	
	# Aktualizovanie UI
	update_puzzle_buttons()
	
	# Zobrazenie úspešného dialógu
	var success_dialogue = dialogue_system.get_puzzle_success_dialogue()
	dialogue_system.start_dialogue(success_dialogue)
	
	# Kontrola, či sú dokončené oba hlavolamy
	if GameManager.is_puzzle_completed(chapter_number, 1) and GameManager.is_puzzle_completed(chapter_number, 2):
		# Kapitola je dokončená
		show_chapter_completion()

func show_chapter_completion():
	await get_tree().create_timer(2.0).timeout
	
	var completion_dialog = AcceptDialog.new()
	add_child(completion_dialog)
	completion_dialog.dialog_text = "Gratulujeme! Dokončili ste " + GameManager.chapter_info[chapter_number].title + "!"
	completion_dialog.title = "Kapitola dokončená"
	completion_dialog.popup_centered()
	
	completion_dialog.confirmed.connect(_on_chapter_completed)

func _on_chapter_completed():
	# Návrat do menu kapitol
	GameManager.go_to_chapters()

func _on_hint_pressed():
	var hint_dialogue = dialogue_system.get_puzzle_hint_dialogue()
	dialogue_system.start_dialogue(hint_dialogue)

func _on_back_pressed():
	GameManager.go_to_chapters()

func _input(event):
	if event.is_action_pressed("ui_cancel") and not dialogue_system.visible:
		_on_back_pressed()
