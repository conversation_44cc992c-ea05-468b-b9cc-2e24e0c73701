extends Control

@onready var dialogue_system: DialogueSystem = $DialogueSystem
@onready var epilogue_title: Label = $VBoxContainer/EpilogueTitle
@onready var back_button: Button = $VBoxContainer/BackButton

func _ready():
	epilogue_title.text = "Epilóg: Nový začiatok"
	back_button.pressed.connect(_on_back_pressed)
	dialogue_system.dialogue_finished.connect(_on_dialogue_finished)
	
	# Spustenie epilógu
	start_epilogue()
	
	back_button.grab_focus()

func start_epilogue():
	var epilogue_dialogue = [
		{"speaker": "Rozprávač", "text": "Gratulujeme! Dokončili ste všetky kapitoly Prekliateho dedičstva."},
		{"speaker": "Rozprávač", "text": "Všetky záhady boli odhalené a prekliatie je zlomené."},
		{"speaker": "Rozprávač", "text": "<PERSON>rad je teraz váš a môžete v ňom žiť v pokoji."},
		{"speaker": "<PERSON>oz<PERSON>r<PERSON>vač", "text": "Ale pamätajte... každé dedičstvo má svoje tajomstvá."},
		{"speaker": "Rozprávač", "text": "A možno sa raz vrátite, aby ste objavili ešte viac záhad..."},
		{"speaker": "Rozprávač", "text": "Ďakujeme vám za hranie! Dúfame, že ste si užili svoje dobrodružstvo."}
	]
	dialogue_system.start_dialogue(epilogue_dialogue)

func _on_dialogue_finished():
	# Epilóg je dokončený, hráč môže ísť späť
	pass

func _on_back_pressed():
	GameManager.go_to_chapters()

func _input(event):
	if event.is_action_pressed("ui_cancel") and not dialogue_system.visible:
		_on_back_pressed()
