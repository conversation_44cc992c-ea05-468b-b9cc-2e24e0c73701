extends Control
class_name DialogueSystem

signal dialogue_finished
signal dialogue_advanced

@onready var dialogue_panel: NinePatchRect = $DialoguePanel
@onready var speaker_label: Label = $DialoguePanel/VBoxContainer/SpeakerLabel
@onready var text_label: RichTextLabel = $DialoguePanel/VBoxContainer/TextLabel
@onready var continue_button: Button = $DialoguePanel/VBoxContainer/ContinueButton

var current_dialogue: Array[Dictionary] = []
var current_line_index: int = 0
var is_typing: bool = false
var typing_speed: float = 0.05

func _ready():
	hide()

	# Bezpečné pripojenie signálov
	if continue_button:
		continue_button.pressed.connect(_on_continue_pressed)

	# Nastavenie štýlu dialógového panelu
	if dialogue_panel:
		dialogue_panel.modulate = Color(1, 1, 1, 0.95)

func start_dialogue(dialogue_data: Array[Dictionary]):
	current_dialogue = dialogue_data
	current_line_index = 0
	show()
	display_current_line()

func display_current_line():
	if current_line_index >= current_dialogue.size():
		end_dialogue()
		return

	var line_data = current_dialogue[current_line_index]
	if speaker_label:
		speaker_label.text = line_data.get("speaker", "Rozprávač")

	# Animácia písania textu
	if text_label:
		text_label.text = ""
	is_typing = true
	if continue_button:
		continue_button.disabled = true

	var full_text = line_data.get("text", "")
	type_text(full_text)

func type_text(text: String):
	if text_label:
		text_label.text = ""

	for i in range(text.length()):
		if not is_typing:
			break
		if text_label:
			text_label.text += text[i]
		await get_tree().create_timer(typing_speed).timeout

	is_typing = false
	if continue_button:
		continue_button.disabled = false
		continue_button.grab_focus()

func _on_continue_pressed():
	if is_typing:
		# Preskočiť animáciu písania
		is_typing = false
		if text_label and current_line_index < current_dialogue.size():
			text_label.text = current_dialogue[current_line_index].get("text", "")
		if continue_button:
			continue_button.disabled = false
		return

	current_line_index += 1
	dialogue_advanced.emit()
	display_current_line()

func end_dialogue():
	hide()
	dialogue_finished.emit()

func _input(event):
	if visible and event.is_action_pressed("ui_accept"):
		_on_continue_pressed()
	elif visible and event.is_action_pressed("ui_cancel"):
		skip_dialogue()

func skip_dialogue():
	is_typing = false
	end_dialogue()

# Prednastavené dialógy pre rozprávača
func get_chapter_intro_dialogue(chapter_number: int) -> Array[Dictionary]:
	var dialogues = {
		1: [
			{"speaker": "Rozprávač", "text": "Vitajte v prvej kapitole vášho dobrodružstva..."},
			{"speaker": "Rozprávač", "text": "Pred vami sa rozprestierajú záhady, ktoré čakajú na svoje riešenie."},
			{"speaker": "Rozprávač", "text": "Buďte opatrní, každé rozhodnutie má svoje následky."}
		],
		2: [
			{"speaker": "Rozprávač", "text": "Druhá kapitola vás zavedie hlbšie do tajomstiev..."},
			{"speaker": "Rozprávač", "text": "Temnota sa zahusťuje a záhady sa komplikujú."},
			{"speaker": "Rozprávač", "text": "Dôverujte svojmu inštinktu."}
		],
		3: [
			{"speaker": "Rozprávač", "text": "Starodávne múry skrývajú mnoho tajomstiev..."},
			{"speaker": "Rozprávač", "text": "Rytieri a alchymisti zanechali svoje stopy."},
			{"speaker": "Rozprávač", "text": "Dokážete rozlúštiť ich posolstvá?"}
		],
		4: [
			{"speaker": "Rozprávač", "text": "Mágia preniká vzduchom..."},
			{"speaker": "Rozprávač", "text": "Runy ožívajú a kúzelné kruhy sa aktivujú."},
			{"speaker": "Rozprávač", "text": "Buďte pripravení na nadprirodzené výzvy."}
		],
		5: [
			{"speaker": "Rozprávač", "text": "Blížite sa k finále..."},
			{"speaker": "Rozprávač", "text": "Čas sa krúti a realita sa mení."},
			{"speaker": "Rozprávač", "text": "Posledné prekážky sú tie najnáročnejšie."}
		],
		6: [
			{"speaker": "Rozprávač", "text": "Konečné odhalenie je na dosah..."},
			{"speaker": "Rozprávač", "text": "Všetky záhady sa spájajú do jedného celku."},
			{"speaker": "Rozprávač", "text": "Ste pripravení na pravdu?"}
		]
	}
	
	return dialogues.get(chapter_number, [{"speaker": "Rozprávač", "text": "Kapitola sa načítava..."}])

func get_puzzle_intro_dialogue(chapter_number: int, puzzle_number: int) -> Array[Dictionary]:
	return [
		{"speaker": "Rozprávač", "text": "Pred vami sa objavuje nový hlavolam..."},
		{"speaker": "Rozprávač", "text": "Pozorne si preštudujte všetky detaily."},
		{"speaker": "Rozprávač", "text": "Riešenie môže byť jednoduchšie, ako sa zdá."}
	]

func get_puzzle_success_dialogue() -> Array[Dictionary]:
	var success_messages = [
		[{"speaker": "Rozprávač", "text": "Výborne! Úspešne ste vyriešili hlavolam."}],
		[{"speaker": "Rozprávač", "text": "Bravó! Vaša logika vás neviedla."}],
		[{"speaker": "Rozprávač", "text": "Skvelé! Ďalšia záhada je odhalená."}]
	]
	return success_messages[randi() % success_messages.size()]

func get_puzzle_hint_dialogue() -> Array[Dictionary]:
	var hints = [
		[{"speaker": "Rozprávač", "text": "Možno by ste sa mali pozrieť na problém z iného uhla..."}],
		[{"speaker": "Rozprávač", "text": "Pamätajte, nie vždy je prvé riešenie to správne."}],
		[{"speaker": "Rozprávač", "text": "Skúste sa sústrediť na detaily, ktoré ste možno prehliadli."}]
	]
	return hints[randi() % hints.size()]
